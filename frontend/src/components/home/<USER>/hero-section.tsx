'use client';
import { HeroVideoSection } from '@/components/home/<USER>/hero-video-section';
import { siteConfig } from '@/lib/home';
import { ArrowRight, Github, X, AlertCircle } from 'lucide-react';
import { FlickeringGrid } from '@/components/home/<USER>/flickering-grid';
import { useMediaQuery } from '@/hooks/use-media-query';
import { useState, useEffect, useRef, FormEvent } from 'react';
import { useScroll } from 'motion/react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';
import {
  createProject,
  createThread,
  addUserMessage,
  startAgent,
  BillingError,
} from '@/lib/api';
import { generateThreadName } from '@/lib/actions/threads';
import GoogleSignIn from '@/components/GoogleSignIn';
import { Input } from '@/components/ui/input';
import { SubmitButton } from '@/components/ui/submit-button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogOverlay,
} from '@/components/ui/dialog';
import { BillingErrorAlert } from '@/components/billing/usage-limit-alert';
import { useBillingError } from '@/hooks/useBillingError';
import { useAccounts } from '@/hooks/use-accounts';
import { isLocalMode, config } from '@/lib/config';
import { toast } from 'sonner';
import { useModal } from '@/hooks/use-modal-store';

// Custom dialog overlay with blur effect
const BlurredDialogOverlay = () => (
  <DialogOverlay className="bg-background/40 backdrop-blur-md" />
);

// Constant for localStorage key to ensure consistency
const PENDING_PROMPT_KEY = 'pendingAgentPrompt';

export function HeroSection() {
  const { hero } = siteConfig;
  const tablet = useMediaQuery('(max-width: 1024px)');
  const [mounted, setMounted] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const scrollTimeout = useRef<NodeJS.Timeout | null>(null);
  const { scrollY } = useScroll();
  const [inputValue, setInputValue] = useState('');
  const router = useRouter();
  const { user, isLoading } = useAuth();
  const { billingError, handleBillingError, clearBillingError } =
    useBillingError();
  const { data: accounts } = useAccounts();
  const personalAccount = accounts?.find((account) => account.personal_account);
  const { onOpen } = useModal();

  // Auth dialog state
  const [authDialogOpen, setAuthDialogOpen] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Detect when scrolling is active to reduce animation complexity
  useEffect(() => {
    const unsubscribe = scrollY.on('change', () => {
      setIsScrolling(true);

      // Clear any existing timeout
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }

      // Set a new timeout
      scrollTimeout.current = setTimeout(() => {
        setIsScrolling(false);
      }, 300); // Wait 300ms after scroll stops
    });

    return () => {
      unsubscribe();
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
    };
  }, [scrollY]);

  // Store the input value when auth dialog opens
  useEffect(() => {
    if (authDialogOpen && inputValue.trim()) {
      localStorage.setItem(PENDING_PROMPT_KEY, inputValue.trim());
    }
  }, [authDialogOpen, inputValue]);

  // Close dialog and redirect when user authenticates
  useEffect(() => {
    if (authDialogOpen && user && !isLoading) {
      setAuthDialogOpen(false);
      router.push('/dashboard');
    }
  }, [user, isLoading, authDialogOpen, router]);

  // Create an agent with the provided prompt
  const createAgentWithPrompt = async () => {
    if (!inputValue.trim() || isSubmitting) return;

    setIsSubmitting(true);

    try {
      // Generate a name for the project using GPT
      const projectName = await generateThreadName(inputValue);

      // 1. Create a new project with the GPT-generated name
      const newAgent = await createProject({
        name: projectName,
        description: '',
      });

      // 2. Create a new thread for this project
      const thread = await createThread(newAgent.id);

      // 3. Add the user message to the thread
      await addUserMessage(thread.thread_id, inputValue.trim());

      // 4. Start the agent with the thread ID
      await startAgent(thread.thread_id, {
        stream: true,
      });

      // 5. Navigate to the new agent's thread page
      router.push(`/agents/${thread.thread_id}`);
      // Clear input on success
      setInputValue('');
    } catch (error: any) {
      console.error('Error creating agent:', error);

      // Check specifically for BillingError (402)
      if (error instanceof BillingError) {
        console.log('Handling BillingError from hero section:', error.detail);
        // Open the payment required dialog modal instead of showing the alert
        onOpen('paymentRequiredDialog');
        // Don't show toast for billing errors
      } else {
        // Handle other errors (e.g., network, other API errors)
        const isConnectionError =
          error instanceof TypeError &&
          error.message.includes('Failed to fetch');
        if (!isLocalMode() || isConnectionError) {
          toast.error(
            error.message || 'Failed to create agent. Please try again.',
          );
        }
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e?: FormEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation(); // Stop event propagation to prevent dialog closing
    }

    if (!inputValue.trim() || isSubmitting) return;

    // If user is not logged in, save prompt and show auth dialog
    if (!user && !isLoading) {
      // Save prompt to localStorage BEFORE showing the dialog
      localStorage.setItem(PENDING_PROMPT_KEY, inputValue.trim());
      setAuthDialogOpen(true);
      return;
    }

    // User is logged in, create the agent
    createAgentWithPrompt();
  };

  // Handle Enter key press
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault(); // Prevent default form submission
      e.stopPropagation(); // Stop event propagation
      handleSubmit();
    }
  };

  // Handle auth form submission
  const handleSignIn = async (prevState: any, formData: FormData) => {
    setAuthError(null);
    try {
      // Implement sign in logic here
      const email = formData.get('email') as string;
      const password = formData.get('password') as string;

      // Add the returnUrl to the form data for proper redirection
      formData.append('returnUrl', '/dashboard');

      // Call your authentication function here

      // Return any error state
      return { message: 'Invalid credentials' };
    } catch (error) {
      console.error('Sign in error:', error);
      setAuthError(
        error instanceof Error ? error.message : 'An error occurred',
      );
      return { message: 'An error occurred during sign in' };
    }
  };

  return (
    <section
      id="hero"
      className="w-full relative overflow-hidden flex flex-col justify-center items-center text-center text-white min-h-screen py-16 px-8 z-10"
    >
      <div className="relative z-10 pt-12 md:pt-20 max-w-3xl mx-auto h-full w-full flex flex-col gap-6 items-center justify-center">
        <div className="flex flex-col items-center justify-center gap-5">
          <h1 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold tracking-tighter text-balance text-center">
            Recrutez un Super Agent IA général !
          </h1>
          <p className="text-lg md:text-xl text-center text-neutral-300 font-medium text-balance leading-relaxed tracking-tight max-w-2xl">
            Votre concierge privé IA s'occupe de 90% du travail pour que vous
            puissiez vous concentrer sur ce qui compte vraiment : votre
            croissance.
          </p>
          <p className="text-md md:text-lg text-center text-neutral-400 font-normal text-balance leading-relaxed tracking-tight max-w-2xl bg-green-500/10 p-4 rounded-lg border-l-4 border-green-500">
            Un Super agent IA général créé pour accompagner les PME et répondre
            à tous vos besoins professionnels. Le tout sécurisé et hébergé en
            France.
          </p>
        </div>
        <div className="flex items-center w-full max-w-xl gap-2 flex-wrap justify-center">
          <form className="w-full relative" onSubmit={handleSubmit}>
            <div className="relative z-10">
              <div className="flex items-center rounded-full border border-border bg-background/80 dark:bg-neutral-900/80 backdrop-blur px-4 shadow-lg transition-all duration-200 hover:border-green-500/50 focus-within:border-green-500/50 focus-within:shadow-[0_0_15px_rgba(0,214,143,0.3)]">
                <input
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Ex: Crée-moi un site web pour ma boulangerie artisanale..."
                  className="flex-1 h-12 md:h-14 rounded-full px-2 bg-transparent focus:outline-none text-sm md:text-base py-2 text-primary dark:text-neutral-200 placeholder:text-muted-foreground dark:placeholder:text-neutral-500"
                  disabled={isSubmitting}
                />

                <button
                  type="submit"
                  className={`rounded-full p-2 md:p-3 transition-all duration-200 ${
                    inputValue.trim()
                      ? 'bg-green-500 text-black hover:bg-green-600'
                      : 'bg-neutral-700 text-neutral-400'
                  }`}
                  disabled={!inputValue.trim() || isSubmitting}
                  aria-label="Submit"
                >
                  {isSubmitting ? (
                    <div className="h-4 md:h-5 w-4 md:w-5 border-2 border-black border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <ArrowRight className="size-4 md:size-5" />
                  )}
                </button>
              </div>
            </div>
            <div className="absolute -bottom-4 inset-x-0 h-6 bg-green-500/20 blur-xl rounded-full -z-10 opacity-70"></div>
          </form>
        </div>
      </div>

      <Dialog open={authDialogOpen} onOpenChange={setAuthDialogOpen}>
        <BlurredDialogOverlay />
        <DialogContent className="sm:max-w-md rounded-xl bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] border border-border">
          <DialogHeader>
            <div className="flex items-center justify-between">
              <DialogTitle className="text-xl font-medium">
                Sign in to continue
              </DialogTitle>
              {/* <button 
                  onClick={() => setAuthDialogOpen(false)}
                  className="rounded-full p-1 hover:bg-muted transition-colors"
                 >
                  <X className="h-4 w-4 text-muted-foreground" />
                 </button> */}
            </div>
            <DialogDescription className="text-muted-foreground">
              Connectez-vous ou créez un compte pour discuter avec Alex
            </DialogDescription>
          </DialogHeader>

          {/* Auth error message */}
          {authError && (
            <div className="mb-4 p-3 rounded-lg flex items-center gap-3 bg-secondary/10 border border-secondary/20 text-secondary">
              <AlertCircle className="h-5 w-5 flex-shrink-0 text-secondary" />
              <span className="text-sm font-medium">{authError}</span>
            </div>
          )}

          {/* Google Sign In */}
          <div className="w-full">
            <GoogleSignIn returnUrl="/dashboard" />
          </div>

          {/* Divider */}
          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-border"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] text-muted-foreground">
                or continue with email
              </span>
            </div>
          </div>

          {/* Sign in form */}
          <form className="space-y-4">
            <div>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="Email address"
                className="h-12 rounded-full bg-background border-border"
                required
              />
            </div>

            <div>
              <Input
                id="password"
                name="password"
                type="password"
                placeholder="Password"
                className="h-12 rounded-full bg-background border-border"
                required
              />
            </div>

            <div className="space-y-4 pt-4">
              <SubmitButton
                formAction={handleSignIn}
                className="w-full h-12 rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md"
                pendingText="Signing in..."
              >
                Sign in
              </SubmitButton>

              <Link
                href={`/auth?mode=signup&returnUrl=${encodeURIComponent('/dashboard')}`}
                className="flex h-12 items-center justify-center w-full text-center rounded-full border border-border bg-background hover:bg-accent/20 transition-all"
                onClick={() => setAuthDialogOpen(false)}
              >
                Create new account
              </Link>
            </div>

            <div className="text-center pt-2">
              <Link
                href={`/auth?returnUrl=${encodeURIComponent('/dashboard')}`}
                className="text-sm text-primary hover:underline"
                onClick={() => setAuthDialogOpen(false)}
              >
                More sign in options
              </Link>
            </div>
          </form>

          <div className="mt-4 text-center text-xs text-muted-foreground">
            By continuing, you agree to our{' '}
            <Link href="/terms" className="text-primary hover:underline">
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link href="/privacy" className="text-primary hover:underline">
              Privacy Policy
            </Link>
          </div>
        </DialogContent>
      </Dialog>

      <BillingErrorAlert
        message={billingError?.message}
        currentUsage={billingError?.currentUsage}
        limit={billingError?.limit}
        accountId={personalAccount?.account_id}
        onDismiss={clearBillingError}
        isOpen={!!billingError}
      />
    </section>
  );
}
