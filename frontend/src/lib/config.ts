// Environment mode types
export enum EnvMode {
  LOCAL = 'local',
  STAGING = 'staging',
  PRODUCTION = 'production',
}

// Subscription tier structure
export interface SubscriptionTierData {
  priceId: string;
  name: string;
}

// Subscription tiers structure
export interface SubscriptionTiers {
  FREE: SubscriptionTierData;
  TIER_2_20: SubscriptionTierData;
  TIER_6_50: SubscriptionTierData;
  TIER_12_100: SubscriptionTierData;
  TIER_25_200: SubscriptionTierData;
  TIER_50_400: SubscriptionTierData;
  TIER_125_800: SubscriptionTierData;
  TIER_200_1000: SubscriptionTierData;
}

// Configuration object
interface Config {
  ENV_MODE: EnvMode;
  IS_LOCAL: boolean;
  SUBSCRIPTION_TIERS: SubscriptionTiers;
}

// Production tier IDs
const PROD_TIERS: SubscriptionTiers = {
  FREE: {
    priceId: 'price_1RILb4G6l1KZGqIrK4QLrx9i',
    name: 'Gratuit',
  },
  TIER_2_20: {
    priceId: 'price_1RILb4G6l1KZGqIrhomjgDnO',
    name: 'Étudiant',
  },
  TIER_6_50: {
    priceId: 'price_1RILb4G6l1KZGqIr5q0sybWn',
    name: 'Essentiel',
  },
  TIER_12_100: {
    priceId: 'price_1RILb4G6l1KZGqIr5Y20ZLHm',
    name: 'Pro',
  },
  TIER_25_200: {
    priceId: 'price_1RILb4G6l1KZGqIrGAD8rNjb',
    name: 'Avancé',
  },
  TIER_50_400: {
    priceId: 'price_1RILb4G6l1KZGqIruNBUMTF1',
    name: 'Entreprise',
  },
  TIER_125_800: {
    priceId: 'price_1RILb3G6l1KZGqIrbJA766tN',
    name: 'Premium',
  },
  TIER_200_1000: {
    priceId: 'price_1RILb3G6l1KZGqIrmauYPOiN',
    name: 'Ultimate',
  },
} as const;

// Staging tier IDs
const STAGING_TIERS: SubscriptionTiers = {
  FREE: {
    priceId: 'price_1RIGvuG6l1KZGqIrw14abxeL',
    name: 'Gratuit',
  },
  TIER_2_20: {
    priceId: 'price_1RIGvuG6l1KZGqIrCRu0E4Gi',
    name: 'Étudiant',
  },
  TIER_6_50: {
    priceId: 'price_1RIGvuG6l1KZGqIrvjlz5p5V',
    name: 'Essentiel',
  },
  TIER_12_100: {
    priceId: 'price_1RIGvuG6l1KZGqIrT6UfgblC',
    name: 'Pro',
  },
  TIER_25_200: {
    priceId: 'price_1RIGvuG6l1KZGqIrOVLKlOMj',
    name: 'Avancé',
  },
  TIER_50_400: {
    priceId: 'price_1RIKNgG6l1KZGqIrvsat5PW7',
    name: 'Entreprise',
  },
  TIER_125_800: {
    priceId: 'price_1RIKNrG6l1KZGqIrjKT0yGvI',
    name: 'Premium',
  },
  TIER_200_1000: {
    priceId: 'price_1RIKQ2G6l1KZGqIrum9n8SI7',
    name: 'Ultimate',
  },
} as const;

// Determine the environment mode from environment variables
const getEnvironmentMode = (): EnvMode => {
  // Get the environment mode from the environment variable, if set
  const envMode = process.env.NEXT_PUBLIC_ENV_MODE?.toLowerCase();

  // First check if the environment variable is explicitly set
  if (envMode) {
    if (envMode === EnvMode.LOCAL) {
      return EnvMode.LOCAL;
    } else if (envMode === EnvMode.STAGING) {
      return EnvMode.STAGING;
    } else if (envMode === EnvMode.PRODUCTION) {
      return EnvMode.PRODUCTION;
    }
  }

  // If no valid environment mode is set, fall back to defaults based on NODE_ENV
  if (process.env.NODE_ENV === 'development') {
    return EnvMode.LOCAL;
  } else {
    return EnvMode.PRODUCTION;
  }
};

// Get the environment mode once to ensure consistency
const currentEnvMode = getEnvironmentMode();

// Create the config object
export const config: Config = {
  ENV_MODE: currentEnvMode,
  IS_LOCAL: currentEnvMode === EnvMode.LOCAL,
  SUBSCRIPTION_TIERS:
    currentEnvMode === EnvMode.STAGING ? STAGING_TIERS : PROD_TIERS,
};

// Helper function to check if we're in local mode (for component conditionals)
export const isLocalMode = (): boolean => {
  return config.IS_LOCAL;
};

// Export subscription tier type for typing elsewhere
export type SubscriptionTier = keyof typeof PROD_TIERS;
