'use client';

import { FooterSection } from '@/components/home/<USER>/footer-section';
import AuroraBackground from '@/components/home/<USER>/AuroraBackground';
import Link from 'next/link';
import { ModalProviders } from '@/providers/modal-providers';
import { ChevronLeftIcon } from '@radix-ui/react-icons';
import { useState } from 'react';
import Image from 'next/image';

export default function ContactPage() {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [company, setCompany] = useState('');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    // Simulation d'envoi du formulaire
    setTimeout(() => {
      setSubmitting(false);
      setSubmitted(true);
      setName('');
      setEmail('');
      setCompany('');
      setSubject('');
      setMessage('');
    }, 1500);
  };

  return (
    <>
      <main className="flex flex-col items-center justify-center min-h-screen w-full">
        <div className="w-full max-w-6xl mx-auto pt-24 px-4 sm:px-6 pb-16">
          <div className="flex items-center mb-6">
            <Link
              href="/"
              className="flex items-center text-primary hover:underline"
            >
              <ChevronLeftIcon className="mr-1" />
              Retour à l&apos;accueil
            </Link>
          </div>

          <h1 className="text-4xl font-bold mb-8 text-center">
            Contactez-nous
          </h1>

          <div className="text-center mb-12">
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Vous avez des questions sur Orchestra Connect ou vous souhaitez en
              savoir plus sur comment notre agent IA Alex peut aider votre
              entreprise ? N&apos;hésitez pas à nous contacter.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-20">
            <div className="bg-card/30 backdrop-blur-sm border border-border rounded-xl p-6">
              <h2 className="text-2xl font-semibold mb-6">
                Envoyez-nous un message
              </h2>

              {submitted ? (
                <div className="text-center py-8">
                  <div className="inline-flex items-center justify-center bg-primary/10 text-primary rounded-full p-3 mb-4">
                    <svg
                      className="w-8 h-8"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M5 13l4 4L19 7"
                      ></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-medium mb-2">Message envoyé !</h3>
                  <p className="text-muted-foreground mb-6">
                    Merci de nous avoir contactés. Notre équipe vous répondra
                    dans les plus brefs délais.
                  </p>
                  <button
                    onClick={() => setSubmitted(false)}
                    className="text-primary hover:underline font-medium"
                  >
                    Envoyer un autre message
                  </button>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label
                      htmlFor="name"
                      className="block text-sm font-medium mb-1"
                    >
                      Nom complet *
                    </label>
                    <input
                      id="name"
                      type="text"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      className="w-full px-4 py-2 rounded-md border border-input bg-background text-foreground"
                      placeholder="Votre nom et prénom"
                      required
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium mb-1"
                    >
                      Email professionnel *
                    </label>
                    <input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full px-4 py-2 rounded-md border border-input bg-background text-foreground"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="company"
                      className="block text-sm font-medium mb-1"
                    >
                      Entreprise *
                    </label>
                    <input
                      id="company"
                      type="text"
                      value={company}
                      onChange={(e) => setCompany(e.target.value)}
                      className="w-full px-4 py-2 rounded-md border border-input bg-background text-foreground"
                      placeholder="Nom de votre entreprise"
                      required
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="subject"
                      className="block text-sm font-medium mb-1"
                    >
                      Sujet *
                    </label>
                    <select
                      id="subject"
                      value={subject}
                      onChange={(e) => setSubject(e.target.value)}
                      className="w-full px-4 py-2 rounded-md border border-input bg-background text-foreground"
                      required
                    >
                      <option value="">Sélectionnez un sujet</option>
                      <option value="demo">Demande de démo</option>
                      <option value="quote">Demande de devis</option>
                      <option value="information">Renseignements</option>
                      <option value="partnership">Partenariat</option>
                      <option value="other">Autre</option>
                    </select>
                  </div>

                  <div>
                    <label
                      htmlFor="message"
                      className="block text-sm font-medium mb-1"
                    >
                      Message *
                    </label>
                    <textarea
                      id="message"
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      rows={5}
                      className="w-full px-4 py-2 rounded-md border border-input bg-background text-foreground"
                      placeholder="Comment pouvons-nous vous aider ?"
                      required
                    ></textarea>
                  </div>

                  <div className="flex items-start mt-2">
                    <input
                      id="privacy"
                      type="checkbox"
                      className="mt-1 mr-2"
                      required
                    />

                    <label
                      htmlFor="privacy"
                      className="text-sm text-muted-foreground"
                    >
                      J&apos;accepte que mes données soient traitées
                      conformément à la{' '}
                      <Link
                        href="/legal/confidentialite"
                        className="text-primary hover:underline"
                      >
                        politique de confidentialité
                      </Link>{' '}
                      d&apos;Orchestra Connect. *
                    </label>
                  </div>

                  <div>
                    <button
                      type="submit"
                      disabled={submitting}
                      className="w-full bg-primary text-white font-medium px-6 py-3 rounded-md hover:bg-primary/90 transition-colors disabled:opacity-70 flex items-center justify-center"
                    >
                      {submitting ? (
                        <>
                          <svg
                            className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          Envoi en cours...
                        </>
                      ) : (
                        'Envoyer le message'
                      )}
                    </button>
                  </div>
                </form>
              )}
            </div>

            <div className="flex flex-col">
              <h2 className="text-2xl font-semibold mb-6">Nos coordonnées</h2>

              <div className="mb-8">
                <h3 className="text-xl font-medium mb-3">Adresse</h3>
                <p className="text-muted-foreground mb-1">
                  ORCHESTRA INTELLIGENCE
                </p>
                <p className="text-muted-foreground">
                  [Votre Adresse Postale Complète - À COMPLÉTER PAR VOUS]
                </p>
                <div className="mt-4 relative h-48 w-full rounded-lg overflow-hidden bg-muted flex items-center justify-center">
                  <div className="text-center text-muted-foreground">
                    <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <p className="text-sm">Carte de localisation</p>
                  </div>
                </div>
              </div>

              <div className="mb-8">
                <h3 className="text-xl font-medium mb-3">Contact</h3>
                <div className="flex items-center mb-2">
                  <svg
                    className="w-5 h-5 text-primary mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    ></path>
                  </svg>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-sm hover:text-primary transition-colors"
                  >
                    <EMAIL>
                  </a>
                </div>
                <div className="flex items-center">
                  <svg
                    className="w-5 h-5 text-primary mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    ></path>
                  </svg>
                  <span className="text-sm">01 23 45 67 89</span>
                </div>
              </div>

              <div>
                <h3 className="text-xl font-medium mb-3">Suivez-nous</h3>
                <div className="flex space-x-4">
                  <a
                    href="#"
                    className="bg-muted p-2 rounded-full hover:bg-primary/10 transition-colors"
                  >
                    <svg
                      className="w-5 h-5 text-primary"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                    </svg>
                  </a>
                  <a
                    href="#"
                    className="bg-muted p-2 rounded-full hover:bg-primary/10 transition-colors"
                  >
                    <svg
                      className="w-5 h-5 text-primary"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path d="M19 3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14m-.5 15.5v-5.3a3.26 3.26 0 0 0-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 0 1 1.4 1.4v4.93h2.79M6.88 8.56a1.68 1.68 0 0 0 1.68-1.68c0-.93-.75-1.69-1.68-1.69a1.69 1.69 0 0 0-1.69 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37h2.77z"></path>
                    </svg>
                  </a>
                  <a
                    href="#"
                    className="bg-muted p-2 rounded-full hover:bg-primary/10 transition-colors"
                  >
                    <svg
                      className="w-5 h-5 text-primary"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"></path>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-card border border-border rounded-xl p-8 mb-16 text-center">
            <h2 className="text-2xl font-semibold mb-4">
              Vous préférez voir Alex en action ?
            </h2>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Planifiez une démo personnalisée avec l&apos;un de nos experts
              pour découvrir comment Orchestra Connect peut transformer votre
              quotidien professionnel.
            </p>
            <Link
              href="#"
              className="bg-primary text-white font-medium px-6 py-3 rounded-md inline-block hover:bg-primary/90 transition-colors"
            >
              Réserver une démo
            </Link>
          </div>
        </div>

        <FooterSection />
      </main>
    </>
  );
}
